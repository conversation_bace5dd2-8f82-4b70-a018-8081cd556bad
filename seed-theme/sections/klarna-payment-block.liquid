

<section class="klarna-payment-section" {{ section.shopify_attributes }}>
  <div class="klarna-payment-container">
    <div class="klarna-payment-content">
      <div class="klarna-text-content">
        <h2 class="klarna-title" id="klarna-title-{{ section.id }}">
          {{ section.settings.title | default: "A no fuss shopping experience." }}
        </h2>

        <p class="klarna-description" id="klarna-description-{{ section.id }}">
          {{ section.settings.description | default: "Shop now. Pay later." }}
        </p>
      </div>
      
      <button class="klarna-button" onclick="openKlarnaModal()" aria-label="Open Klarna payment options">
        <svg xmlns="http://www.w3.org/2000/svg" class="klarna-badge" viewBox="0 0 118.64 59.32">
          <rect width="118.64" height="59.32" rx="8.9" ry="8.9"/>
          <path fill="#fff" d="M94.12 25.75v.86a7.1 7.1 0 1 0 0 11.74v.86h4V25.75Zm-3.67 10.19a3.47 3.47 0 1 1 3.65-3.46 3.56 3.56 0 0 1-3.65 3.46m-58.73-16.2h-4.37a11.2 11.2 0 0 1-4.52 9l-1.73 1.32 6.71 9.15h5.52l-6.18-8.42a15.46 15.46 0 0 0 4.57-11.05m-15.63 0h4.48v19.47h-4.48zm18.54.01h4.22v19.46h-4.22zm41.23 5.63a4.88 4.88 0 0 0-4.15 1.88v-1.51h-4v13.46h4.06v-7.08a2.85 2.85 0 0 1 3-3.05c1.77 0 2.79 1.06 2.79 3v7.11h4v-8.54c.04-3.14-2.46-5.27-5.7-5.27m-24.26.37v.86a7.1 7.1 0 1 0 0 11.74v.86h4V25.75Zm-3.67 10.19a3.47 3.47 0 1 1 3.66-3.46 3.56 3.56 0 0 1-3.66 3.46m13.93-8.44v-1.75h-4.12v13.46h4.13v-6.29c0-2.12 2.3-3.26 3.9-3.26v-3.91a5 5 0 0 0-3.91 1.75m40.57 6.93A2.53 2.53 0 1 0 105 37a2.53 2.53 0 0 0-2.57-2.57"/>
        </svg>
      </button>
    </div>
  </div>
</section>

<!-- Klarna Modal -->
<div id="klarna-modal" class="klarna-modal" onclick="closeKlarnaModal(event)">
  <div class="klarna-modal-content" onclick="event.stopPropagation()">
    <div class="klarna-modal-header">
      <div class="klarna-logo">
        <svg viewBox="0 0 60 30" class="klarna-logo-svg">
          <path fill="#FFB3C7" d="M0 0h60v30H0z"/>
          <text x="30" y="20" text-anchor="middle" fill="#000" font-family="Arial, sans-serif" font-size="12" font-weight="bold">Klarna</text>
        </svg>
      </div>
      <button class="klarna-close-btn" onclick="closeKlarnaModal()" aria-label="Close modal">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>
    </div>
    
    <div class="klarna-modal-body">
      <h3>{{ section.settings.modal_title | default: "Flexible payment options" }}</h3>
      
      <div class="klarna-payment-options">
        <div class="klarna-option">
          <div class="klarna-option-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z" fill="#FFB3C7"/>
            </svg>
          </div>
          <div class="klarna-option-content">
            <h4>{{ section.settings.option_1_title | default: "Pay in 4" }}</h4>
            <p>{{ section.settings.option_1_description | default: "Interest free, due every 14 days" }}</p>
          </div>
        </div>
        
        <div class="klarna-option">
          <div class="klarna-option-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" fill="#FFB3C7"/>
            </svg>
          </div>
          <div class="klarna-option-content">
            <h4>{{ section.settings.option_2_title | default: "Pay now" }}</h4>
            <p>{{ section.settings.option_2_description | default: "Pay in full with card" }}</p>
          </div>
        </div>
      </div>
      
      <div class="klarna-info">
        <p>{{ section.settings.modal_info | default: "The availability of payment options depends on the merchant and the purchase amount. Approval is required. Additional terms may apply." }}</p>
      </div>
      
      <div class="klarna-steps">
        <h4>{{ section.settings.steps_title | default: "How it works" }}</h4>
        <ul>
          <li>{{ section.settings.step_1 | default: "At checkout select Klarna" }}</li>
          <li>{{ section.settings.step_2 | default: "Choose your payment plan" }}</li>
          <li>{{ section.settings.step_3 | default: "Complete your checkout" }}</li>
        </ul>
      </div>
      
      <button class="klarna-modal-close-btn" onclick="closeKlarnaModal()">
        {{ section.settings.close_button_text | default: "Close" }}
      </button>
    </div>
  </div>
</div>

<style>
/* Klarna Payment Section */
.klarna-payment-section {
  width: 100%;
  padding: 5px 0px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent;
}

.klarna-payment-container {
  max-width: 380px;
  width: 100%;
}

.klarna-payment-content {
  background-color: {{ section.settings.background_color | default: '#FEB3C7' }};
  border-radius: {{ section.settings.border_radius | default: 4 }}px;
  padding: 40px 40px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  min-height: 436px;
  position: relative;
  box-sizing: border-box;
}

.klarna-text-content {
  margin-bottom: 40px;
  width: 100%;
}

.klarna-title {
  font-family: {{ section.settings.title_font | default: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif' }};
  font-size: {{ section.settings.title_size | default: 44 }}px;
  font-weight: {{ section.settings.title_weight | default: 700 }};
  line-height: {{ section.settings.title_line_height | default: 1 }};
  letter-spacing: {{ section.settings.title_letter_spacing | default: -0.94 }}px;
  color: {{ section.settings.title_color | default: '#000000' }};
  margin: 0 0 20px 0;
  text-align: left;
  opacity: 1;
  transition: opacity {{ section.settings.fade_duration | default: 0.5 }}s ease-in-out;
}

.klarna-description {
  font-family: {{ section.settings.description_font | default: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif' }};
  font-size: {{ section.settings.description_size | default: 20 }}px;
  color: {{ section.settings.description_color | default: '#000000' }};
  margin: 0;
  line-height: normal;
  text-align: left;
  opacity: 1;
  transition: opacity {{ section.settings.fade_duration | default: 0.5 }}s ease-in-out;
}

.klarna-title.fade-out,
.klarna-description.fade-out {
  opacity: 0;
}

.klarna-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  align-self: flex-end;
}

.klarna-badge {
  width: {{ section.settings.button_width | default: 118 }}px;
  height: auto;
  display: block;
}

/* Modal Styles */
.klarna-modal {
  display: none;
  position: fixed;
  z-index: 10000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.klarna-modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.klarna-modal-content {
  background: white;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease;
}

.klarna-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e5e5;
}

.klarna-logo-svg {
  width: 60px;
  height: 30px;
}

.klarna-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.klarna-close-btn:hover {
  background-color: #f5f5f5;
}

.klarna-modal-body {
  padding: 24px;
}

.klarna-modal-body h3 {
  margin: 0 0 24px 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.klarna-payment-options {
  margin-bottom: 24px;
}

.klarna-option {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
}

.klarna-option-icon {
  margin-right: 12px;
  flex-shrink: 0;
}

.klarna-option-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.klarna-option-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.klarna-info {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.klarna-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.klarna-steps h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.klarna-steps ul {
  margin: 0;
  padding-left: 20px;
}

.klarna-steps li {
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.klarna-modal-close-btn {
  width: 100%;
  background: #2a1b3d;
  color: white;
  border: none;
  padding: 16px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
  margin-top: 16px;
}

.klarna-modal-close-btn:hover {
  background: #1a0f2e;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .klarna-payment-section {
    padding: 5px 15px;
  }
  
  .klarna-payment-content {
    padding: 30px 20px;
    min-height: 350px;
  }
  
  .klarna-title {
    font-size: {{ section.settings.title_size_mobile | default: 32 }}px;
    min-height: 100px;
  }
  
  .klarna-description {
    font-size: {{ section.settings.description_size_mobile | default: 16 }}px;
  }
  
  .klarna-badge {
    width: {{ section.settings.button_width_mobile | default: 100 }}px;
  }
  
  .klarna-modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .klarna-modal-header {
    padding: 16px 20px;
  }
  
  .klarna-modal-body {
    padding: 20px;
  }
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>

<script>
// Text Animation
(function() {
  const sectionId = '{{ section.id }}';
  const animationInterval = {{ section.settings.animation_interval | default: 10 }} * 1000; // Convert to milliseconds
  const fadeDuration = {{ section.settings.fade_duration | default: 0.5 }} * 1000; // Convert to milliseconds
  const titleDelay = {{ section.settings.title_delay | default: 0.2 }} * 1000; // Delay between title and description fade

  function animateText() {
    const titleElement = document.getElementById(`klarna-title-${sectionId}`);
    const descriptionElement = document.getElementById(`klarna-description-${sectionId}`);

    if (!titleElement || !descriptionElement) return;

    // Fade out title first
    titleElement.classList.add('fade-out');

    // Fade out description after a short delay
    setTimeout(() => {
      descriptionElement.classList.add('fade-out');
    }, titleDelay);

    // Fade both back in after fade duration
    setTimeout(() => {
      titleElement.classList.remove('fade-out');
      descriptionElement.classList.remove('fade-out');
    }, fadeDuration + titleDelay);
  }

  // Start animation cycle
  if (animationInterval > 0) {
    setInterval(animateText, animationInterval);
  }
})();

// Modal Functions
function openKlarnaModal() {
  const modal = document.getElementById('klarna-modal');
  if (modal) {
    modal.classList.add('active');
    document.body.style.overflow = 'hidden';
  }
}

function closeKlarnaModal(event) {
  if (event && event.target !== event.currentTarget) return;
  
  const modal = document.getElementById('klarna-modal');
  if (modal) {
    modal.classList.remove('active');
    document.body.style.overflow = '';
  }
}

// Close modal on Escape key
document.addEventListener('keydown', function(event) {
  if (event.key === 'Escape') {
    closeKlarnaModal();
  }
});
</script>

{% schema %}
{
  "name": "Klarna Payment Block",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "A no fuss shopping experience."
    },
    {
      "type": "text",
      "id": "description",
      "label": "Description",
      "default": "Shop now. Pay later."
    },
    {
      "type": "header",
      "content": "Animation Settings"
    },
    {
      "type": "range",
      "id": "animation_interval",
      "label": "Animation Interval (seconds)",
      "min": 5,
      "max": 30,
      "step": 1,
      "default": 10,
      "info": "How often the text fades in and out"
    },
    {
      "type": "range",
      "id": "fade_duration",
      "label": "Fade Duration (seconds)",
      "min": 0.3,
      "max": 2,
      "step": 0.1,
      "default": 0.5,
      "info": "How long the fade transition takes"
    },
    {
      "type": "range",
      "id": "title_delay",
      "label": "Title to Description Delay (seconds)",
      "min": 0.1,
      "max": 1,
      "step": 0.1,
      "default": 0.2,
      "info": "Delay between title and description fade out"
    },
    {
      "type": "header",
      "content": "Design Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#FEB3C7"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "Border Radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 4
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "label": "Title Size (Desktop)",
      "min": 24,
      "max": 60,
      "step": 2,
      "unit": "px",
      "default": 44
    },
    {
      "type": "range",
      "id": "title_size_mobile",
      "label": "Title Size (Mobile)",
      "min": 20,
      "max": 40,
      "step": 2,
      "unit": "px",
      "default": 32
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title Color",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "description_size",
      "label": "Description Size (Desktop)",
      "min": 14,
      "max": 28,
      "step": 2,
      "unit": "px",
      "default": 20
    },
    {
      "type": "range",
      "id": "description_size_mobile",
      "label": "Description Size (Mobile)",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "color",
      "id": "description_color",
      "label": "Description Color",
      "default": "#000000"
    },
    {
      "type": "header",
      "content": "Button Settings"
    },
    {
      "type": "range",
      "id": "button_width",
      "label": "Button Width (Desktop)",
      "min": 80,
      "max": 200,
      "step": 5,
      "unit": "px",
      "default": 120
    },
    {
      "type": "range",
      "id": "button_width_mobile",
      "label": "Button Width (Mobile)",
      "min": 70,
      "max": 150,
      "step": 5,
      "unit": "px",
      "default": 100
    },
    {
      "type": "header",
      "content": "Modal Content"
    },
    {
      "type": "text",
      "id": "modal_title",
      "label": "Modal Title",
      "default": "Flexible payment options"
    },
    {
      "type": "text",
      "id": "option_1_title",
      "label": "Payment Option 1 Title",
      "default": "Pay in 4"
    },
    {
      "type": "text",
      "id": "option_1_description",
      "label": "Payment Option 1 Description",
      "default": "Interest free, due every 14 days"
    },
    {
      "type": "text",
      "id": "option_2_title",
      "label": "Payment Option 2 Title",
      "default": "Pay now"
    },
    {
      "type": "text",
      "id": "option_2_description",
      "label": "Payment Option 2 Description",
      "default": "Pay in full with card"
    },
    {
      "type": "textarea",
      "id": "modal_info",
      "label": "Modal Info Text",
      "default": "The availability of payment options depends on the merchant and the purchase amount. Approval is required. Additional terms may apply."
    },
    {
      "type": "text",
      "id": "steps_title",
      "label": "Steps Title",
      "default": "How it works"
    },
    {
      "type": "text",
      "id": "step_1",
      "label": "Step 1",
      "default": "At checkout select Klarna"
    },
    {
      "type": "text",
      "id": "step_2",
      "label": "Step 2",
      "default": "Choose your payment plan"
    },
    {
      "type": "text",
      "id": "step_3",
      "label": "Step 3",
      "default": "Complete your checkout"
    },
    {
      "type": "text",
      "id": "close_button_text",
      "label": "Close Button Text",
      "default": "Close"
    }
  ]
}
{% endschema %}
